// content/utils.ts

// URL matching utilities
export const isEbaySearchPage = (url: string): boolean => {
  return /^https:\/\/www\.ebay\.[^\/]+\/sch\//.test(url) || /^https:\/\/www\.ebay\.[^\/]+\/b\/[^\/]+/.test(url);
};

export const isEbayItemPage = (url: string): boolean => {
  return /^https:\/\/www\.ebay\.[^\/]+\/itm\//.test(url);
};

// DOM manipulation utilities
export const insertAfter = (newNode: HTMLElement, referenceNode: HTMLElement): void => {
  referenceNode.parentNode?.insertBefore(newNode, referenceNode.nextSibling);
};

export const removeElementsByClassName = (className: string): void => {
  const elements = document.querySelectorAll(`.${className}`);
  elements.forEach(element => element.remove());
};

// Style injection utility
export const injectStyles = (styles: string): void => {
  const styleElement = document.createElement('style');
  styleElement.textContent = styles;
  document.head.appendChild(styleElement);
};

// Chrome message sending utility
export const sendChromeMessage = (message: any): Promise<any> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, response => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(response);
      }
    });
  });
};
