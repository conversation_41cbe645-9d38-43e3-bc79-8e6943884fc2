import React, { useState } from 'react';
import Checkbox from './Checkbox';
import type { CountryInfo } from '../files/types';

interface CountrySubRegionProps {
  subRegion: string;
  countries: CountryInfo[];
  removedCountries: Record<string, boolean>;
  isSubRegionSelected: (subRegion: string) => boolean;
  isSubRegionIndeterminate: (subRegion: string) => boolean;
  toggleSubRegion: (subRegion: string) => void;
  toggleCountry: (countryId: number) => void;
}

const CountrySubRegion: React.FC<CountrySubRegionProps> = ({
  subRegion,
  countries,
  removedCountries,
  isSubRegionSelected,
  isSubRegionIndeterminate,
  toggleSubRegion,
  toggleCountry,
}) => {
  // Local state to manage if this particular sub-region is open or closed
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div 
      tabIndex={0} 
      className={`collapse collapse-arrow border border-base-300 bg-base-200 mb-2 ${isOpen ? 'collapse-open' : 'collapse-close'}`}
    >
      <div
        className="collapse-title flex items-center"
        onClick={() => setIsOpen(!isOpen)} // Toggle open state on click
      >
        <input
          type="checkbox"
          id={`subregion-${subRegion}`}
          className="checkbox checkbox-primary mr-2"
          checked={isSubRegionSelected(subRegion)}
          ref={(el) => {
            if (el) {
              el.indeterminate = isSubRegionIndeterminate(subRegion);
            }
          }}
          onChange={() => toggleSubRegion(subRegion)}
        />
        <label htmlFor={`subregion-${subRegion}`} className="font-bold text-lg">
          {subRegion}
        </label>
      </div>

      {isOpen && (
        <div className="collapse-content">
          <div className="grid grid-cols-4 gap-2">
            {countries.map((country) => (
              <Checkbox
                key={country.id}
                id={`country-${country.id}`}
                label={country.name}
                checked={removedCountries[country.id] || false}
                onChange={() => toggleCountry(country.id)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CountrySubRegion;
