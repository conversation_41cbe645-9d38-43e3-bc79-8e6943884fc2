{
  // "typescript.tsserver.experimental.enableProjectDiagnostics": true,
  "workbench.editor.enablePreview": false,
  "files.exclude": {
    "**/.git": true, // this is a default value
    "**/.DS_Store": true, // this is a default value
    "**/node_modules": false, // this excludes all folders
    // alternative version
    "node_modules": false,
    ".prettierignore": true,
    ".prettierrc.json": true,
    "package-lock.json": false,
    // "package.json": true,
    "img": true,
    // ".vscode": true,
    "dist": true,
    //
    "temp_frontend_webpack": true,
    "temp_extension_webpack": true,
    "temp_extension_build": true,
    "temp_extension_install": true,
    "temp_ext_build": true,
    "temp_ext_webpack": true,
    "temp_back": true
  }
}
