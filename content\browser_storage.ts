import type { UserSettings, BrowserStorage } from "./files/types";

let browser_storage_default: BrowserStorage = {
  session: null,
  browser_user_settings: null,
  supabase_user_settings: null,
};

async function get_browser_storage(filter?: Array<string>) {
  let storage: any = await chrome.storage.local.get(filter || null);
  // defaults
  for (let key in browser_storage_default) {
    if (storage[key] === undefined) {
      // @ts-ignore
      storage[key] = browser_storage_default[key];
    }
  }

  // return
  return storage as BrowserStorage;
}

async function set_browser_storage(data: any) {
  await chrome.storage.local.set(data);
}

export { get_browser_storage, set_browser_storage };
