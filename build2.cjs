let { execSync } = require("child_process");
let archiver = require("archiver");
let fs = require("fs");
let fs_extra = require("fs-extra");
let path = require("path");

function zip_dir(input_dir, output_dir, output_file) {
  return new Promise((resolve) => {
    fs.rmSync(output_dir, { recursive: true, force: true });
    fs_extra.ensureDirSync(output_dir);

    var output = fs.createWriteStream(path.resolve(output_dir, output_file));
    var archive = archiver("zip");

    output.on("close", function () {
      console.log(output_file);
      console.log(archive.pointer() + " total bytes");
      console.log("archiver has been finalized and the output file descriptor has closed.");
      resolve(true);
    });

    archive.on("error", function (err) {
      console.log("archiver error", err);
      resolve(false);
    });

    archive.pipe(output);

    // append files from a sub-directory, putting its contents at the root of archive
    archive.directory(input_dir, false);

    // append files from a sub-directory and naming it `new-subdir` within the archive
    archive.directory("subdir/", "new-subdir");

    archive.finalize();
  });
}

async function build() {
  execSync("npm run build", {
    cwd: ".",
    stdio: "inherit",
    shell: true,
  });
  execSync("npm run build1", {
    cwd: ".",
    stdio: "inherit",
    shell: true,
  });
  let manifest_json = JSON.parse(fs.readFileSync("./manifest.json", "utf-8"));
  await zip_dir("./dist/chrome", "./dist/zip", `ubuyassist-${manifest_json.version}.zip`);
  console.log("build complete");
}

build();
