import React from "react";


const Checkbox: React.FC<{
  id: string;
  label: string;
  checked: boolean;
  disabled?: boolean;
  onChange: () => void;
}> = ({ id, label, checked, onChange, disabled }) => (
  <div className="mb-2 flex items-center">
    {/* Adjust alignment with flex */}
    <input type="checkbox" id={id} className="checkbox checkbox-xs" checked={checked} onChange={onChange} disabled={!disabled}/>
    <label htmlFor={id} className="ml-2 leading-tight">
      {/* Use leading-tight for better alignment */}
      {label}
    </label>
  </div>
);

export default Checkbox;
