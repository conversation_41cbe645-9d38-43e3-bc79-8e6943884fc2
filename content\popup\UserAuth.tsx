import React, { useState } from "react";
import supabase from "../../helper/supabaseClient";
import { useUserSettings } from "../../helper/UserSettingsContext";
import { set_browser_storage } from "../browser_storage";
import { console_log, wait } from "../../helper/helpers";
import SessionManager from "../../helper/SessionManager";

const UserAuth = ({ signup = false, onToggleView }: { signup?: boolean; onToggleView: (isSignupView: boolean) => void }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { load_supabase_user_settings, set_loading_status, setSession } = useUserSettings();

  const handleSignUp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setErrorMessage(null);
    //
    set_loading_status({
      status: "active",
      type: "alert-info",
      message: "Creating account...",
    });
    //
    const { data, error } = await supabase.auth.signUp({
      email: email,
      password: password,
    });
    if (error) {
      console_log(typeof error.message);
      setErrorMessage(error.message);
      set_loading_status({
        status: "not_active",
        type: "alert-error",
        message: "Could not log in.",
      });
      return;
    }
    if (data?.session) {
      console_log("Good work you are in", data);
      const user = {
        token: data.session.access_token,
        ...data.user,
      };
      // Store session in chrome browser storage
      let session = {
        token: data.session.access_token,
        refreshToken: data.session.refresh_token,
        id: data.session.user.id,
      };

      // Update SessionManager with new session
      const sessionManager = SessionManager.getInstance();
      sessionManager.setCurrentSession(session);

      setSession(user as any);
      set_browser_storage({ session });
      load_supabase_user_settings(session.id);
      //
      set_loading_status({
        status: "active",
        type: "alert-success",
        message: "Logged in.",
      });
      await wait(1500);
      set_loading_status({
        status: "not_active",
        type: "alert-success",
        message: "Logged in.",
      });
      //
    }
  };

  const handleSignIn = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setErrorMessage(null);
    //
    set_loading_status({
      status: "active",
      type: "alert-info",
      message: "Loggin in...",
    });
    //
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    });
    if (error) {
      setErrorMessage(error.message);
      set_loading_status({
        status: "not_active",
        type: "alert-error",
        message: "Could not log in.",
      });
      return;
    }
    if (data?.session) {
      console_log("Good work you are in", data);
      const user = {
        token: data.session.access_token,
        refreshToken: data.session.refresh_token,
        ...data.user,
      };
      //
      let session = {
        token: data.session.access_token,
        refreshToken: data.session.refresh_token,
        id: data.user.id,
      };

      // Update SessionManager with new session
      const sessionManager = SessionManager.getInstance();
      sessionManager.setCurrentSession(session);

      setSession(user as any);
      set_browser_storage({ session });
      load_supabase_user_settings(session.id);
      //
      set_loading_status({
        status: "active",
        type: "alert-success",
        message: "Logged in.",
      });
      await wait(1500);
      set_loading_status({
        status: "not_active",
        type: "alert-success",
        message: "Logged in.",
      });
      //
    }
  };

  return (
    <>
      <p className="text-lg font-bold mb-2 text-center">{signup ? "Register" : "Log in"}</p>
      <form
        onSubmit={signup ? handleSignUp : handleSignIn}
        className="w-full max-w-sm mx-auto bg-white/10 p-6 rounded-lg shadow-lg my-4"
      >
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-1">Email</label>
          <input
            type="email"
            placeholder="Enter your email"
            className="w-full p-3 bg-transparent border border-gray-400 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-1">Password</label>
          <input
            type="password"
            placeholder="Enter your password"
            className="w-full p-3 bg-transparent border border-gray-400 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>
        <p className="text-red-500 mt-2 mb-1">{errorMessage}</p>

        <button
          type="submit"
          className="w-full bg-blue-500 text-white font-semibold py-3 rounded-lg shadow-md hover:bg-blue-600 transition"
        >
          {signup ? "Register" : "Log in"}
        </button>
      </form>
      <p onClick={() => onToggleView(!signup)} className="text-sm font-regular text-center my-4">
        <span className="font-bold underline cursor-pointer">{!signup ? "Register" : "Log in"}</span> if you already
        have an account
      </p>
    </>
  );
};

export default UserAuth;
