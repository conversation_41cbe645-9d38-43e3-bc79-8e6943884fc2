// Debug script to monitor session state
// Run this in the browser console to see what's happening with sessions

console.log("=== SESSION DEBUG SCRIPT ===");

// Function to check current session state
async function checkSessionState() {
  console.log("\n--- Current Session State ---");
  
  // Check browser storage
  const storage = await chrome.storage.local.get(['session']);
  console.log("Browser Storage Session:", storage.session);
  
  // Check SessionManager if available
  if (typeof SessionManager !== 'undefined') {
    const sessionManager = SessionManager.getInstance();
    const currentSession = sessionManager.getCurrentSession();
    console.log("SessionManager Current Session:", currentSession);
    
    const validSession = await sessionManager.getValidSession();
    console.log("SessionManager Valid Session:", validSession);
  } else {
    console.log("SessionManager not available in this context");
  }
  
  // Check Supabase session
  if (typeof supabase !== 'undefined') {
    const { data: { session } } = await supabase.auth.getSession();
    console.log("Supabase Current Session:", session ? {
      user_id: session.user.id,
      access_token: session.access_token.substring(0, 20) + "...",
      refresh_token: session.refresh_token
    } : null);
  } else {
    console.log("Supabase not available in this context");
  }
}

// Function to monitor storage changes
function monitorStorageChanges() {
  console.log("\n--- Monitoring Storage Changes ---");
  chrome.storage.onChanged.addListener((changes, namespace) => {
    if (changes.session) {
      console.log("🔄 Session changed in storage:");
      console.log("Old:", changes.session.oldValue);
      console.log("New:", changes.session.newValue);
      
      if (changes.session.oldValue && changes.session.newValue) {
        const oldToken = changes.session.oldValue.refreshToken;
        const newToken = changes.session.newValue.refreshToken;
        if (oldToken !== newToken) {
          console.log("🔑 Refresh token changed:");
          console.log("From:", oldToken);
          console.log("To:", newToken);
        }
      }
    }
  });
}

// Function to monitor Supabase auth state changes
function monitorSupabaseAuth() {
  if (typeof supabase !== 'undefined') {
    console.log("\n--- Monitoring Supabase Auth Changes ---");
    supabase.auth.onAuthStateChange((event, session) => {
      console.log("🔐 Supabase auth state change:", event);
      if (session) {
        console.log("Session details:", {
          user_id: session.user.id,
          access_token: session.access_token.substring(0, 20) + "...",
          refresh_token: session.refresh_token,
          expires_at: new Date(session.expires_at * 1000).toISOString()
        });
      }
    });
  }
}

// Run initial check
checkSessionState();

// Start monitoring
monitorStorageChanges();
monitorSupabaseAuth();

// Provide helper functions
window.debugSession = {
  check: checkSessionState,
  clearSession: async () => {
    await chrome.storage.local.set({ session: null });
    console.log("✅ Session cleared from storage");
  },
  forceRefresh: async () => {
    if (typeof SessionManager !== 'undefined') {
      const sessionManager = SessionManager.getInstance();
      await sessionManager.forceRefreshFromStorage();
      console.log("✅ SessionManager force refreshed");
    }
  }
};

console.log("\n--- Debug Functions Available ---");
console.log("debugSession.check() - Check current session state");
console.log("debugSession.clearSession() - Clear session from storage");
console.log("debugSession.forceRefresh() - Force refresh SessionManager");
console.log("=== END DEBUG SCRIPT ===\n");
