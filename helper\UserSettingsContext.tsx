import React, { create<PERSON>ontext, useContext, useEffect, useReducer, useState } from "react";
import { get_browser_storage, set_browser_storage } from "../content/browser_storage";
import { default_user_settings, type UserSettings } from "../content/files/types";
import supabase from "./supabaseClient";
import { console_log, wait } from "./helpers";
import SessionManager from "./SessionManager";

type LoadingStatus = {
  status: "active" | "not_active";
  // type corresponds to class names of the UI library
  // https://daisyui.com/components/alert/?lang=en
  type: "alert-info" | "alert-success" | "alert-error" | "alert-warning";
  message: string;
};

interface UserSettingsContextType {
  userSettings: UserSettings;
  loading_status: LoadingStatus;
  setUserSettings: (settings: UserSettings) => void;
  update_user_settings: (settings: UserSettings) => void;
  load_supabase_user_settings: (user_id: string) => void;
  set_loading_status: (loading_status: LoadingStatus) => void;
  session: string | null;
  setSession: (session: string | null) => void;
}

type UserSettingsStore = {
  status: "initial" | "normal" | "updating";
  userSettings: UserSettings;
};

const UserSettingsContext = createContext<UserSettingsContextType | undefined>(undefined);

export const UserSettingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  let default_loading_status: LoadingStatus = {
    status: "not_active",
    type: "alert-info",
    message: "Default",
  };
  const [userSettings, setUserSettings] = useState<UserSettings>(default_user_settings);
  const [loading_status, set_loading_status] = useState<LoadingStatus>(default_loading_status);
  const [session, setSession] = useState<any | null>(null);

  // Load usersettings from chrome browser storage on mount
  let initialized_flag = false;
  useEffect(() => {
    let handler = async () => {
      let browser_storage = await get_browser_storage(["session", "browser_user_settings", "supabase_user_settings"]);
      if (browser_storage.session) {
        try {
          // Use SessionManager to safely initialize session
          const sessionManager = SessionManager.getInstance();
          const validSession = await sessionManager.initializeSession();

          if (validSession) {
            // Fetch UserSettings from the database
            const { data, error } = await supabase
              .from("UserSettings")
              .select("settings")
              .eq("user_id", validSession.id);

            if (error) {
              console.error("Error fetching UserSettings:", error);
              throw error;
            }

            let rows: any = data;
            console_log("rows", rows);

            // Check if we got any results
            if (rows && rows.length > 0 && rows[0].settings) {
              console_log("settings", rows[0].settings);
              setUserSettings(rows[0].settings);
            } else {
              console_log("No UserSettings found for user, using defaults");
              // Fall back to browser storage or defaults
              if (browser_storage.supabase_user_settings) {
                setUserSettings(browser_storage.supabase_user_settings);
              } else if (browser_storage.browser_user_settings) {
                setUserSettings(browser_storage.browser_user_settings);
              } else {
                setUserSettings(default_user_settings);
              }
              return; // Skip realtime setup if no valid settings
            }

            // Set up realtime subscription
            supabase
              .channel("table_db_changes")
              .on(
                "postgres_changes",
                {
                  event: "*",
                  schema: "public",
                  table: "UserSettings",
                },
                async (payload: any) => {
                  console_log("payload", payload);
                  // payload.new.settings
                  setUserSettings(payload.new.settings);
                  // set_browser_storage({ supabase_user_settings: payload.new.settings });
                },
              )
              .subscribe();
          } else {
            console_log("No valid session, using browser storage");
            // No valid session, use browser storage
            if (browser_storage.supabase_user_settings) {
              setUserSettings(browser_storage.supabase_user_settings);
            } else if (browser_storage.browser_user_settings) {
              setUserSettings(browser_storage.browser_user_settings);
            } else {
              setUserSettings(default_user_settings);
            }
          }
        } catch (error) {
          console.error("Error loading user settings:", error);
          // Fall back to browser storage
          if (browser_storage.supabase_user_settings) {
            setUserSettings(browser_storage.supabase_user_settings);
          } else if (browser_storage.browser_user_settings) {
            setUserSettings(browser_storage.browser_user_settings);
          } else {
            setUserSettings(default_user_settings);
          }
        }
      } else if (browser_storage.supabase_user_settings) {
        console_log("Using browser_storage.supabase_user_settings");
        setUserSettings(browser_storage.supabase_user_settings);
        console_log(browser_storage.supabase_user_settings);
        //
      } else if (browser_storage.browser_user_settings) {
        console_log("Using browser_storage.browser_user_settings");
        setUserSettings(browser_storage.browser_user_settings);
        console_log(browser_storage.browser_user_settings);
        //
      } else {
        console_log("Using default_user_settings");
        setUserSettings(default_user_settings);
        console_log(default_user_settings);
        //
      }
    };
    if (initialized_flag === false) {
      initialized_flag = true;
      handler();
    }
  }, []);

  // Load session data on mount and listen for storage changes
  let session_initialized_flag = false;
  useEffect(() => {
    let handler = async () => {
      const sessionManager = SessionManager.getInstance();
      await sessionManager.forceRefreshFromStorage();
      let browser_storage = await get_browser_storage(["session"]);
      setSession(browser_storage.session);
    };

    // Listen for storage changes to keep session synchronized
    const storageListener = (changes: any) => {
      if (changes.session) {
        console_log("Session changed in storage, updating context");
        const sessionManager = SessionManager.getInstance();
        sessionManager.forceRefreshFromStorage();
        setSession(changes.session.newValue);
      }
    };

    if (session_initialized_flag === false) {
      session_initialized_flag = true;
      handler();
      chrome.storage.onChanged.addListener(storageListener);
    }

    // Cleanup listener on unmount
    return () => {
      chrome.storage.onChanged.removeListener(storageListener);
    };
  }, []);

  // useEffect(() => {
  // todo store user settings either in Supabase or in ChromeStorage here
  // }, [userSettings]);

  let update_user_settings = (new_user_settings: UserSettings) => {
    console_log("update_user_settings", new_user_settings);
    // console_log("update context", userSettings);
    setUserSettings(new_user_settings);
    set_loading_status({
      status: "active",
      type: "alert-info",
      message: "Updating...",
    });
    let handler = async () => {
      const sessionManager = SessionManager.getInstance();
      const validSession = await sessionManager.getValidSession();

      if (validSession) {
        console_log("start", Date.now());
        try {
          let result = await supabase.from("UserSettings").upsert({
            user_id: validSession.id,
            settings: new_user_settings,
          });
          console_log("result", result);
          console_log("end", Date.now());
          await set_browser_storage({ supabase_user_settings: new_user_settings });
        } catch (error: any) {
          console.error("Error updating UserSettings:", error);
          // If session is invalid, clear it and fall back to browser storage
          if (error?.message && error.message.includes("JWT")) {
            await sessionManager.clearSession();
            await set_browser_storage({ browser_user_settings: new_user_settings });
          }
        }
      } else {
        await set_browser_storage({ browser_user_settings: new_user_settings });
      }
      //
      set_loading_status({
        status: "active",
        type: "alert-success",
        message: "Changes saved!",
      });
      await wait(1200);
      set_loading_status({
        status: "not_active",
        type: "alert-success",
        message: "Changes saved!",
      });
    };
    handler();
  };

  let load_supabase_user_settings = async (user_id: string) => {
    const { data, error } = await supabase.from("UserSettings").select("settings").eq("user_id", user_id);
    console_log("data,error", data, error);
    let rows: any = data;
    // handling the use case when the row is not present
    if (rows[0] === undefined) {
      // store browser_user_settings if user settings are not present in the database
      let storage = await get_browser_storage(["browser_user_settings"]);
      let new_user_settings = storage.browser_user_settings || default_user_settings;
      new_user_settings.lastClickTime = Date.now();
      // todo: handle error result gracefully
      let result = await supabase.from("UserSettings").upsert({
        user_id: user_id,
        settings: new_user_settings,
      });
      //
      setUserSettings(new_user_settings);
      set_browser_storage({ supabase_user_settings: new_user_settings });
    } else {
      console_log("rows", rows);
      console_log("settings", rows[0].settings);
      setUserSettings(rows[0].settings);
      set_browser_storage({ supabase_user_settings: rows[0].settings });
    }
  };

  return (
    <UserSettingsContext.Provider
      value={{
        // session
        session,
        setSession,
        // user_settings
        userSettings,
        setUserSettings,
        // loading_status
        loading_status,
        set_loading_status,
        //
        update_user_settings,
        load_supabase_user_settings,
      }}
    >
      {children}
    </UserSettingsContext.Provider>
  );
};

// Custom hook to use the UserSettingsContext
export const useUserSettings = (): UserSettingsContextType => {
  const context = useContext(UserSettingsContext);
  if (!context) {
    throw new Error("useUserSettings must be used within a UserSettingsProvider");
  }
  return context;
};
