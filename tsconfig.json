{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["dom", "dom.iterable", "esnext"], "moduleResolution": "node", "module": "esnext", "noEmit": false, "sourceMap": true, "inlineSourceMap": false, "inlineSources": true, "resolveJsonModule": true, "strict": true, "target": "esnext", "verbatimModuleSyntax": true, "useDefineForClassFields": true, "skipLibCheck": true, "outDir": "./dist", "rootDir": "./"}, "include": ["./"], "exclude": ["node_modules", "dist"]}