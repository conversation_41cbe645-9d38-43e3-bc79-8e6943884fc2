import config_json from "../config.json";

let console_log = console.log;

if (config_json.mode === "prod") {
  console_log = () => {};
}

export { console_log };

export async function wait(time: number) {
  return new Promise((resolve: Function) => {
    setTimeout(resolve, time);
  });
}

export function html_to_element(html: string) {
  let div = document.createElement("div");
  div.innerHTML = html;
  return div.firstElementChild as HTMLElement;
}
