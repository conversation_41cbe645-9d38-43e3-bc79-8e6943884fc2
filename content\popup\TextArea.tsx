import React from 'react';

const TextArea: React.FC<{
  id: string;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  rows?: number; // Add optional rows prop
}> = ({ id, placeholder, value, onChange, disabled, rows }) => ( // Destructure rows
  <div className="mb-2">
    <textarea
      className="textarea textarea-bordered w-full mt-2"
      id={id}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      rows={rows} // Pass rows to the textarea element
    ></textarea>
  </div>
);

export default TextArea;
