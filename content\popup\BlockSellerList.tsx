import React, { useState, useEffect } from "react";
import Checkbox from "./Checkbox";
import TextArea from "./TextArea";
import supabase from "../../helper/supabaseClient";
import { get_browser_storage } from "../browser_storage";

import { useUserSettings } from "../../helper/UserSettingsContext";
import { console_log } from "../../helper/helpers";

interface BlockSellerListProps {
  toggleState: boolean;
}

const BlockSellerList: React.FC<BlockSellerListProps> = ({ toggleState }) => {
  const { userSettings, update_user_settings } = useUserSettings();

  const [blockSellerData, setBlockSellerData] = useState<any[]>([]); // State for storing BlockSeller data
  const [selectedSellers, setSelectedSellers] = useState<Set<string>>(new Set()); // For tracking selected sellers
  const [loading, setLoading] = useState<boolean>(true); // Loading state
  const [selectAll, setSelectAll] = useState(false); // State for "Select All" functionality
  const [showBulkAddArea, setShowBulkAddArea] = useState(false); // State for bulk add UI visibility
  const [bulkAddInput, setBulkAddInput] = useState(""); // State for bulk add text area
  const [isAddingBulk, setIsAddingBulk] = useState(false); // Loading state for bulk add
  const [active_section_name, set_active_section_name] = useState("normal");

  useEffect(() => {
    let data = userSettings.blockedSellers.map((name) => {
      return { name };
    });
    setBlockSellerData(data);
  }, [userSettings]);

  useEffect(() => {
    if (userSettings.toggleState && userSettings.blockSellersEnabled) {
      if (userSettings.blockedSellers.length === 0) {
        set_active_section_name("no_items");
      } else {
        set_active_section_name("normal");
      }
    } else {
      set_active_section_name("disabled");
    }
  }, [userSettings]);

  const handleSelectSeller = (seller_name: string) => {
    const newSelectedSellers = new Set(selectedSellers);
    if (newSelectedSellers.has(seller_name)) {
      newSelectedSellers.delete(seller_name);
    } else {
      newSelectedSellers.add(seller_name);
    }
    setSelectedSellers(newSelectedSellers);
  };

  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedSellers(new Set());
    } else {
      const allNames = new Set(blockSellerData.map((seller) => seller.name));
      setSelectedSellers(allNames);
    }
    setSelectAll(!selectAll);
  };

  const removeSellers = async () => {
    // Get selected seller names as an array
    const selectedSellerNames = Array.from(selectedSellers);
    console_log("selectedSellerNames", selectedSellerNames);
    userSettings.blockedSellers = userSettings.blockedSellers.filter((seller_name) => {
      return selectedSellerNames.includes(seller_name) === false;
    });

    update_user_settings({ ...userSettings });
  };

  // --- Bulk Add Handlers ---
  const handleBulkAddClick = () => {
    setShowBulkAddArea(true);
  };

  const handleCancelBulkAdd = () => {
    setShowBulkAddArea(false);
    setBulkAddInput("");
    setIsAddingBulk(false); // Reset loading state on cancel
  };

  const handleAddToList = async () => {
    if (!bulkAddInput.trim()) {
      alert("Please paste sellers into the text area.");
      return;
    }
    setIsAddingBulk(true);
    try {
      const sellersToAdd = bulkAddInput
        .split("\n")
        .map((s) => s.trim())
        .filter((s) => s !== ""); // Split, trim, and remove empty lines

      console_log("sellersToAdd", sellersToAdd);

      for (let seller of sellersToAdd) {
        if (userSettings.blockedSellers.includes(seller) === false) {
          userSettings.blockedSellers.push(seller);
        }
      }

      update_user_settings({ ...userSettings });

      setBulkAddInput(""); // Clear input
      setShowBulkAddArea(false); // Hide area
    } catch (error: any) {
      console.error("Error adding sellers:", error);
      alert(`Error adding sellers: ${error.message || "Unknown error"}`);
    } finally {
      setIsAddingBulk(false); // End loading state
    }
  };

  return (
    <>
      <h6 className="text-sm font-bold mt-6 mb-2">Block Seller List ({userSettings.blockedSellers.length})</h6>
      <Checkbox
        id="enable-block-seller-list"
        label="Enable Block Seller List"
        checked={userSettings.blockSellersEnabled}
        onChange={() =>
          update_user_settings({ ...userSettings, blockSellersEnabled: !userSettings.blockSellersEnabled })
        }
        disabled={toggleState}
      />
      <div className="mb-3"></div>
      {active_section_name === "initial" && <div>Initial</div>}
      {active_section_name === "disabled" && <div>Feature disabled</div>}
      {active_section_name === "no_items" && (
        <div>
          {" "}
          {/* Wrap "No Blocked Sellers" and Bulk Add button */}
          <p>No Blocked Sellers found.</p>
          <button
            className="mt-2 p-2 border rounded-lg text-green-500 border-green-500"
            onClick={handleBulkAddClick}
            disabled={showBulkAddArea}
          >
            Bulk Add Sellers
          </button>
          {/* Conditional Bulk Add Area (also shown when list is empty) */}
          {showBulkAddArea && (
            <div className="mt-4 p-4 border rounded-lg bg-gray-700 space-y-2">
              <label htmlFor="bulk-add-sellers-empty" className="block text-sm font-medium">
                Paste Seller Names (one per line):
              </label>
              <TextArea
                id="bulk-add-sellers-empty"
                placeholder={"seller1\nseller2\nseller3"}
                value={bulkAddInput}
                onChange={(e) => setBulkAddInput(e.target.value)}
                disabled={isAddingBulk}
                rows={5}
              />
              <div className="flex space-x-2">
                <button
                  className="p-2 border rounded-lg bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                  onClick={handleAddToList}
                  disabled={isAddingBulk || !bulkAddInput.trim()}
                >
                  {isAddingBulk ? "Adding..." : "Add to List"}
                </button>
                <button
                  className="p-2 border rounded-lg bg-gray-500 hover:bg-gray-600"
                  onClick={handleCancelBulkAdd}
                  disabled={isAddingBulk}
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
      )}
      {active_section_name === "normal" && (
        <div className="space-y-4">
          {" "}
          {/* Added spacing */}
          {/* Existing List Management */}
          <div>
            <div className="flex space-x-2 mb-4">
              {" "}
              {/* Button row */}
              <button className="p-2 border rounded-lg" onClick={toggleSelectAll}>
                {selectAll ? "Deselect All" : "Select All"}
              </button>
              <button
                className="p-2 border rounded-lg text-red-500 border-red-500"
                disabled={selectedSellers.size === 0}
                onClick={removeSellers}
              >
                Remove Selected ({selectedSellers.size})
              </button>
              <button
                className="p-2 border rounded-lg text-green-500 border-green-500"
                onClick={handleBulkAddClick}
                disabled={showBulkAddArea} // Disable if area is already shown
              >
                Bulk Add Sellers
              </button>
            </div>

            {/* Bulk Add Area (Conditional) */}
            {showBulkAddArea && (
              <div className="p-4 border rounded-lg bg-gray-700 space-y-2">
                <label htmlFor="bulk-add-sellers" className="block text-sm font-medium">
                  Paste Seller Names (one per line):
                </label>
                <TextArea
                  id="bulk-add-sellers"
                  placeholder={"seller1\nseller2\nseller3"}
                  value={bulkAddInput}
                  onChange={(e) => setBulkAddInput(e.target.value)}
                  disabled={isAddingBulk}
                  rows={5} // Adjust rows as needed
                />
                <div className="flex space-x-2">
                  <button
                    className="p-2 border rounded-lg bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                    onClick={handleAddToList}
                    disabled={isAddingBulk || !bulkAddInput.trim()}
                  >
                    {isAddingBulk ? "Adding..." : "Add to List"}
                  </button>
                  <button
                    className="p-2 border rounded-lg bg-gray-500 hover:bg-gray-600"
                    onClick={handleCancelBulkAdd}
                    disabled={isAddingBulk}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}

            {/* Seller List */}
            <ul className="max-h-60 overflow-y-auto border rounded-lg p-2">
              {" "}
              {/* Added scroll */}
              {blockSellerData.map((seller, index) => (
                <li
                  key={seller.name || index} // Use name as key if available and unique
                  className="flex items-center justify-between p-2 mb-1 rounded bg-white/10 hover:bg-white/20"
                >
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-blue-600"
                      checked={selectedSellers.has(seller.name)}
                      onChange={() => handleSelectSeller(seller.name)}
                    />
                    <span className="truncate" title={seller.name}>
                      {seller.name}
                    </span>{" "}
                    {/* Added truncate and title */}
                  </label>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </>
  );
};

export default BlockSellerList;
