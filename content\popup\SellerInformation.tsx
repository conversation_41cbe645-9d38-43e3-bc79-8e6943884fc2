import React from "react";
import Checkbox from "./Checkbox";
import { useUserSettings } from "../../helper/UserSettingsContext";

const SellerInformation: React.FC<{}> = () => {
  const { userSettings, update_user_settings } = useUserSettings();
  return (
    <>
      <h6 className="text-sm font-bold mb-2">Seller Information</h6>
      <Checkbox
        id="show-flag"
        label="Show Flag"
        checked={userSettings.showFlag}
        onChange={
          //
          () => update_user_settings({ ...userSettings, showFlag: !userSettings.showFlag })
        }
        disabled={userSettings.toggleState}
      />
      <Checkbox
        id="show-country-iso"
        label="Show Country Code"
        checked={userSettings.showCountryIso}
        onChange={
          //
          () => update_user_settings({ ...userSettings, showCountryIso: !userSettings.showCountryIso })
        }
        disabled={userSettings.toggleState}
      />
      <Checkbox
        id="show-store-individual"
        label="Show Store/Individual"
        checked={userSettings.showStoreIndividual}
        onChange={
          //
          () => update_user_settings({ ...userSettings, showStoreIndividual: !userSettings.showStoreIndividual })
        }
        disabled={userSettings.toggleState}
      />
      <Checkbox
        id="show-age"
        label="Show Seller Age"
        checked={userSettings.showAge}
        onChange={
          //
          () => update_user_settings({ ...userSettings, showAge: !userSettings.showAge })
        }
        disabled={userSettings.toggleState}
      />
      <Checkbox
        id="show-transaction-percentage"
        label="Show Seller Sell/Buy ratio"
        checked={userSettings.showTransactionPercentage}
        onChange={
          //
          () =>
            update_user_settings({
              ...userSettings,
              showTransactionPercentage: !userSettings.showTransactionPercentage,
            })
        }
        disabled={userSettings.toggleState}
      />
    </>
  );
};

export default SellerInformation;
