// Simple test script to verify SessionManager functionality
// This can be run in the browser console to test the implementation

console.log("Testing SessionManager implementation...");

// Test 1: Singleton pattern
const sessionManager1 = SessionManager.getInstance();
const sessionManager2 = SessionManager.getInstance();
console.log("Singleton test:", sessionManager1 === sessionManager2 ? "PASS" : "FAIL");

// Test 2: Initial state
console.log("Initial session:", sessionManager1.getCurrentSession());

// Test 3: Set and get session
const testSession = {
  id: "test-user-id",
  token: "test-access-token",
  refreshToken: "test-refresh-token"
};

sessionManager1.setCurrentSession(testSession);
console.log("Set session test:", sessionManager1.getCurrentSession() === testSession ? "PASS" : "FAIL");

// Test 4: Clear session
sessionManager1.setCurrentSession(null);
console.log("Clear session test:", sessionManager1.getCurrentSession() === null ? "PASS" : "FAIL");

console.log("SessionManager tests completed!");
