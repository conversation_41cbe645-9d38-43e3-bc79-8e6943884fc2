import { get_seller_name, processSellerElement, get_product_id } from "./sellerUtils";
import {
  default_user_settings,
  ONE_DAY_IN_MS,
  type Settings,
  type SupabaseUserSession,
  type UserSettings,
} from "./files/types";
import { get_browser_storage } from "./browser_storage";

import type { SetStateAction } from "react";
import type { ShortUser } from "./files/User";
import { isEbayItemPage, insertAfter, sendChromeMessage, isEbaySearchPage } from "./utils";
import supabase from "../helper/supabaseClient";

const closeIcon =
  "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjwhLS0gVXBsb2FkZWQgdG86IFNWRyBSZXBvLCB3d3cuc3ZncmVwby5jb20sIEdlbmVyYXRvcjogU1ZHIFJlcG8gTWl4ZXIgVG9vbHMgLS0+CjxzdmcgZmlsbD0iIzAwMDAwMCIgd2lkdGg9IjgwMHB4IiBoZWlnaHQ9IjgwMHB4IiB2aWV3Qm94PSIwIDAgMzUgMzUiIGRhdGEtbmFtZT0iTGF5ZXIgMiIgaWQ9ImIxYmVjMjVhLWE0NDMtNGRhNy1iNDQzLTM5MTZlYTdlYTI0NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMjguODE0LDMwLjA2NGExLjI0NywxLjI0NywwLDAsMS0uODg0LS4zNjdMNS4zLDcuMDdBMS4yNDksMS4yNDksMCwwLDEsNy4wNyw1LjNMMjkuNywyNy45M2ExLjI1MSwxLjI1MSwwLDAsMS0uODg0LDIuMTM0WiIvPjxwYXRoIGQ9Ik02LjE4NiwzMC4wNjRBMS4yNTEsMS4yNTEsMCwwLDEsNS4zLDI3LjkzTDI5LjczLDUuM0ExLjI1LDEuMjUsMCwwLDEsMjkuNyw3LjA3TDcuMDcsMjkuN0ExLjI0NywxLjI0NywwLDAsMSw2LjE4NiwzMC4wNjRaIi8+PC9zdmc+";

const blockIcon =
  "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGQ9Ik01LjYzNjA1IDUuNjM2MDNMMTguMzY0IDE4LjM2NE0yMSAxMkMyMSAxNi45NzA2IDE2Ljk3MDYgMjEgMTIgMjFDNy4wMjk0NCAyMSAzIDE2Ljk3MDYgMyAxMkMzIDcuMDI5NDQgNy4wMjk0NCAzIDEyIDNDMTYuOTcwNiAzIDIxIDcuMDI5NDQgMjEgMTJaIiBzdHJva2U9IiMwMDAwMDAiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4NCjwvc3ZnPg==";

const WatchIcon =
  "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDNi41IDIgMiA2LjUgMiAxMkMyIDE3LjUgNi41IDIyIDEyIDIyQzE3LjUgMjIgMjIgMTcuNSAyMiAxMkMyMiA2LjUgMTcuNSAyIDEyIDJaTTEyIDIwQzcuNTkgMjAgNCAxNi40MSA0IDEyQzQgNy41OSA3LjU5IDQgMTIgNEMxNi40MSA0IDIwIDcuNTkgMjAgMTJDMjAgMTYuNDEgMTYuNDEgMjAgMTIgMjBaTTEyLjUgN0gxMVYxM0wxNi4yIDE2LjJMMTcgMTQuOUwxMi41IDEyLjJWN1oiIGZpbGw9ImJsYWNrIi8+Cjwvc3ZnPgo=";

import { set_browser_storage } from "./browser_storage";
import { html_to_element, console_log } from "../helper/helpers";
import SessionManager from "../helper/SessionManager";

export default class ControllerContent {
  search_result_element_data_arr: Array<{
    product_id: string;
    search_result_element: HTMLElement;
    seller_element: HTMLElement;
    seller_name: string;
    shortUser?: ShortUser;
  }> = [];
  user_settings: UserSettings = default_user_settings;
  browser_user_settings: UserSettings = default_user_settings;
  session: SupabaseUserSession | null = null;
  async init() {
    //
    this.add_listeners();
    this.inject_styles();
    //
    if (isEbaySearchPage(window.location.href) || isEbayItemPage(window.location.href)) {
      let elements = document.querySelectorAll<HTMLElement>(
        `li.s-item, li.su-card-container, [data-testid="x-sellercard-atf"]`,
      );
      console_log("elements", elements);
      //
      for (let element of elements) {
        let { product_id, seller_name, seller_element } = this.get_search_result_element_info(element);
        console_log("search_result_element_info", seller_name, seller_element);
        if (seller_name !== "_not_found_") {
          this.search_result_element_data_arr.push({
            product_id,
            search_result_element: element,
            seller_name,
            seller_element,
          });
        }
      }
      let seller_name_arr = this.search_result_element_data_arr.map((item) => {
        return item.seller_name;
      });
      console_log("seller_name_arr", seller_name_arr);
      let seller_info_data = await this.get_seller_info_from_cache_or_fetch(seller_name_arr);
      console_log("seller_info_data", seller_info_data);
      //
      for (let item of this.search_result_element_data_arr) {
        let { search_result_element, seller_element, seller_name } = item;
        search_result_element.classList.add("ubuyassist-detected-search-result");
        let shortUser = seller_info_data[seller_name];
        item.shortUser = shortUser;
        console_log("shortUser", shortUser);
        processSellerElement(search_result_element, seller_element, seller_name, shortUser, default_user_settings);
      }
      //
      this.load_storage_and_render_initial();
    }
  }
  get_search_result_element_info(element: HTMLElement) {
    let { seller_name, seller_element } = get_seller_name(element);
    let product_id = get_product_id(element);
    return { product_id, seller_name, seller_element };
  }
  inject_styles() {
    // .ubuyassist-hidden {
    //   display: none !important;
    // }
    // todo: move this to a separate css file and import it as a raw string
    let styles = `
      <style>
        .ubuy-seller-info.ubuy-hidden {
          display: none !important;
        }
        
        /* User seller components - hide everything by default */
        [data-s='showFlag'] { display: none !important; }
        [data-s='showCountryIso'] { display: none !important; }
        [data-s='showStoreIndividual'] { display: none !important; }
        [data-s='showAge'] { display: none !important; }
        [data-s='showTransactionPercentage'] { display: none !important; }

        /* User seller components - show elements based on enabled user settings */
        [data-s*='showFlag'] [data-s='showFlag'] { display: inline-block !important; }
        [data-s*='showCountryIso'] [data-s='showCountryIso'] { display: inline-block !important; }
        [data-s*='showStoreIndividual'] [data-s='showStoreIndividual'] { display: inline-block !important; }
        [data-s*='showAge'] [data-s='showAge'] { display: inline-block !important; }
        [data-s*='showTransactionPercentage'] [data-s='showTransactionPercentage'] { display: inline-block !important; }


        .ubuy-delete-buttons-wrapper.ubuy-hidden {
          display: none !important;
        }
        .ubuy-activate-container {
          display: block;
        }
        .ubuyassist-detected-search-result {
          max-height: 600px;
          transition: all 0.4s ease !important;
        }
        .ubuyassist-hidden {
          /* display: none !important; */
          max-height: 1px;
          position: relative;
          overflow: hidden;
          filter: grayscale(1) blur(3px);
          transition: all 0.4s ease !important;
          pointer-events: none;
          user-select: none;
        }
        .ubuyassist-hidden-super {
          /* display: none !important; */
          max-height: 1px;
          position: relative;
          overflow: hidden;
          filter: grayscale(1) blur(3px);
          transition: all 0.4s ease !important;
          pointer-events: none;
          user-select: none;
        }
        .ubuyassist-hidden+.ubuyassist-hidden {
          padding: 0px !important;
          max-height: 1px !important;
        }
        
        .ubuy-activate-button {
          background: none;
          border: none;            
          cursor: pointer;
          font-size: inherit;
          padding: 0;
        }
        .ubuy-activate-button:hover {
            text-decoration: underline;
        }
        .particle {
            position: fixed;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
        }
        @keyframes explode {
            0% {
                transform: translate(0, 0);
                opacity: 1;
            }
            100% {
                transform: translate(var(--tx), var(--ty));
                opacity: 0;
            }
        }
        /* block item button */
        .block-item-btn:hover {
          opacity: 1 !important; /* Ensure visibility */
          background-color: rgba(255, 0, 0, 0.3) !important; /* Subtle red background */
          color:black;
          border-width: 1px;
          /* Red filter - adjust values as needed */
         
        }
        .remove-item-btn:hover {
          opacity: 1 !important; /* Ensure visibility */
          background-color: rgba(255, 255, 0, 0.6) !important; /* Subtle yellow background */
          color:black;
          border-width: 1px;
          /* Yellow filter - adjust values as needed */
        }
        /**/
        /**/
        /**/
        .block-item-btn {
          width: 16px;
          height: 16px;
          cursor: pointer;
          padding: 10px;
          margin-top: -10px;
          margin-left: 10px;
          box-sizing: content-box;
          opacity: 0.3;
          border-radius: 50%;        
          transition: opacity 0.3s ease, background-color 0.3s ease, border-radius 0.3s ease; /* Keep filter transition for smoothness */
        }
        .remove-item-btn {
          width: 16px;
          height: 16px;
          cursor: pointer;
          padding: 10px;
          margin-left: 10px;
          margin-top: 10px;
          box-sizing: content-box;
          opacity: 0.15; /* Increased opacity */
          border-radius: 50%;
          transition: opacity 0.3s ease, background-color 0.3s ease, border-radius 0.3s ease; /* Added filter transition */
        }
        .ubuy-block-seller-button {
          position: absolute;
          display: none;
          z-index: 1000;
          background-color: #ff4757;
          color: white;
          padding: 8px 12px;
          border: none;
          border-radius: 4px;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
          font-size: 13px;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          transition: all 0.2s ease;
        } 

      </style>
    `;
    let element = html_to_element(styles);
    document.documentElement.prepend(element);
  }
  async load_session() {
    if (this.session) {
      try {
        const sessionManager = SessionManager.getInstance();
        const validSession = await sessionManager.initializeSession();

        if (validSession) {
          console_log("Session initialized successfully");

          // Set up realtime subscription
          supabase
            .channel("table_db_changes")
            .on(
              "postgres_changes",
              {
                event: "*",
                schema: "public",
                table: "UserSettings",
              },
              async (payload: any) => {
                console_log("payload", payload);
                // payload.new.settings
                set_browser_storage({ supabase_user_settings: payload.new.settings });
              },
            )
            .subscribe();

          // Fetch UserSettings from the database
          const { data: data_fetch, error } = await supabase
            .from("UserSettings")
            .select("settings")
            .eq("user_id", validSession.id);

          if (error) {
            console.error("Error fetching UserSettings:", error);
            return;
          }

          let rows: any = data_fetch;
          console_log("rows", rows);

          // Check if we got any results
          if (rows && rows.length > 0 && rows[0].settings) {
            console_log("settings", rows[0].settings);
            this.user_settings = rows[0].settings as UserSettings;
            set_browser_storage({ supabase_user_settings: this.user_settings });
            this.render(this.user_settings);
          } else {
            console_log("No UserSettings found for user in ControllerContent");
            // Use default settings if no user settings found
            this.user_settings = default_user_settings;
            this.render(this.user_settings);
          }
        } else {
          console_log("No valid session available");
          // Clear session reference if invalid
          this.session = null;
        }
      } catch (error) {
        console.error("Error in load_session:", error);
        // Clear session on error
        this.session = null;
      }
    }
  }
  async load_storage_and_render_initial() {
    let browser_storage: any = await get_browser_storage([
      "session",
      "browser_user_settings",
      "supabase_user_settings",
    ]);
    console_log("browser_storage", browser_storage);
    this.browser_user_settings = browser_storage.browser_user_settings;
    if (browser_storage.session) {
      this.session = browser_storage.session;
      this.load_session();
    }
    if (browser_storage.supabase_user_settings) {
      this.user_settings = browser_storage.supabase_user_settings;
      this.render(this.user_settings);
      //
    } else if (browser_storage.browser_user_settings) {
      this.user_settings = browser_storage.browser_user_settings;
      this.render(this.user_settings);
      //
    }
  }
  async handle_storage_change() {
    let browser_storage: any = await get_browser_storage([
      "session",
      "browser_user_settings",
      "supabase_user_settings",
    ]);
    console_log("browser_storage", browser_storage);
    this.browser_user_settings = browser_storage.browser_user_settings;

    // Update SessionManager with latest session from storage
    const sessionManager = SessionManager.getInstance();
    await sessionManager.forceRefreshFromStorage();

    //
    if (this.session === null && browser_storage.session) {
      this.session = browser_storage.session;
      this.load_session();
    }
    if (this.session && browser_storage.session && this.session.id !== browser_storage.session.id) {
      this.session = browser_storage.session;
      this.load_session();
    }
    //
    if (browser_storage.supabase_user_settings) {
      this.user_settings = browser_storage.supabase_user_settings;
    } else if (browser_storage.browser_user_settings) {
      this.user_settings = browser_storage.browser_user_settings;
    }
    this.render(this.user_settings);
  }
  async update_supabase_user_settings(new_settings: UserSettings) {
    // todo: add some sort of a toast notification about the result
    const sessionManager = SessionManager.getInstance();
    const validSession = await sessionManager.getValidSession();

    if (validSession) {
      console_log("start", Date.now());
      try {
        let result = await supabase.from("UserSettings").upsert({
          user_id: validSession.id,
          settings: new_settings,
        });
        console_log("result", result);
        console_log("end", Date.now());
      } catch (error: any) {
        console.error("Error updating UserSettings in ControllerContent:", error);
        // If session is invalid, clear it
        if (error?.message && error.message.includes("JWT")) {
          await sessionManager.clearSession();
          this.session = null;
        }
      }
    }
  }
  add_listeners() {
    // Listen for settings changes from popup or other content scripts
    // re-render view on storage changes
    chrome.storage.onChanged.addListener(async (changes) => {
      console_log("chrome.storage.onChanged", changes);
      this.handle_storage_change();
    });

    // Add an event listener for the dynamically created Affiliate activations buttons
    document.addEventListener("click", async (event) => {
      const target = event.target as HTMLElement;
      console_log("click", target, event.composedPath);

      // Create a mapping of eBay domains to their site IDs, excluding ebay.com
      const siteIdMapping: { [key: string]: string } = {
        "www.ebay.ie": "2",
        "www.ebay.at": "3",
        "www.ebay.com.au": "4",
        "www.befr.ebay.be": "5",
        "www.benl.ebay.be": "5",
        "www.ebay.ca": "7",
        "www.ebay.fr": "10",
        "www.ebay.de": "11",
        "www.ebay.it": "12",
        "www.ebay.es": "13",
        "www.ebay.ch": "14",
        "www.ebay.co.uk": "15",
        "www.ebay.nl": "16",
      };

      const currentHost = window.location.hostname;
      const siteid = currentHost === "www.ebay.com" || !siteIdMapping[currentHost] ? "" : siteIdMapping[currentHost];

      // Check if the clicked element has the 'ubuy-activate-button' class
      if (target && target.classList.contains("ubuy-activate-button")) {
        event.preventDefault();

        // Trigger fireworks effect
        this.createFireworks(target);

        // Update user settings
        // Always use browser_user_settings for storing lastClickTime
        //
        this.browser_user_settings.lastClickTime = Date.now();
        this.render(this.user_settings);
        await set_browser_storage({ browser_user_settings: this.browser_user_settings });
        // no need for this render
        // this.render(this.user_settings);

        const extensionVersion = chrome.runtime.getManifest().version;
        const siteidParam = siteid ? `&site=${siteid}` : "";
        // Send a message to the background script to open the link in a background tab
        const url = `https://data.ubuyfirst.net/uBuyFirst/promotion/?v=${extensionVersion}${siteidParam}`;
        chrome.runtime.sendMessage({ action: "openInBackground", url: url });
      } else if (target && target.classList.contains("block-item-btn")) {
        let item_product_id = target.dataset.product_id;
        if (item_product_id) {
          if (this.session) {
            //
            this.user_settings.blockedItems.push(item_product_id);
            this.render(this.user_settings);
            this.update_supabase_user_settings(this.user_settings);
            set_browser_storage({ supabase_user_settings: this.user_settings });
            //
          } else {
            //
            this.user_settings.blockedItems.push(item_product_id);
            this.render(this.user_settings);
            set_browser_storage({ browser_user_settings: this.user_settings });
            //
          }
        }
      } else if (target && target.classList.contains("ubuy-block-seller-button")) {
        let item_seller_name = target.dataset.seller_name;
        if (item_seller_name) {
          if (this.session) {
            //
            this.user_settings.blockedSellers.push(item_seller_name);
            this.render(this.user_settings);
            this.update_supabase_user_settings(this.user_settings);
            await set_browser_storage({ supabase_user_settings: this.user_settings });
            //
          } else {
            //
            this.user_settings.blockedSellers.push(item_seller_name);
            this.render(this.user_settings);
            await set_browser_storage({ browser_user_settings: this.user_settings });
            //
          }
        }
      } else if (target && target.classList.contains("remove-item-btn")) {
        let item_product_id = target.dataset.product_id;
        for (let item of this.search_result_element_data_arr) {
          let { search_result_element, product_id, seller_element, seller_name } = item;
          if (item_product_id === product_id) {
            search_result_element.classList.add("ubuyassist-hidden-super");
          }
        }
      }
      console_log(event.composedPath);
    });
  }
  //
  render(settings: UserSettings) {
    // Disable or re-enable all features based on lastClickTime
    if (this.browser_user_settings.lastClickTime + ONE_DAY_IN_MS < Date.now()) {
      // show affiliate restriction
      for (let item of this.search_result_element_data_arr) {
        let { search_result_element, seller_element, seller_name } = item;
        this.affiliate_show_restriction(search_result_element, seller_element);
        search_result_element.classList.remove("ubuyassist-hidden");
        search_result_element.classList.remove("ubuyassist-hidden-super");
        search_result_element.querySelector(".ubuy-delete-buttons-wrapper")?.classList.add("ubuy-hidden");
        search_result_element.querySelector(".ubuy-seller-info")?.classList.add("ubuy-hidden");
        seller_element.dataset.ubuy_enabled = "false";
      }
      // stop executing the render because all features should be disabled
      // until the user activates the affiliate link again
      return;
    } else {
      // hide affiliate restriction
      for (let item of this.search_result_element_data_arr) {
        let { search_result_element, seller_element, seller_name } = item;
        this.affiliate_hide_restriction(search_result_element, seller_element);
        search_result_element.querySelector(".ubuy-delete-buttons-wrapper")?.classList.remove("ubuy-hidden");
        search_result_element.querySelector(".ubuy-seller-info")?.classList.remove("ubuy-hidden");
        seller_element.dataset.ubuy_enabled = "true";
      }
    }
    // Disable or re-enable all features based on toggleState
    if (settings.toggleState === false) {
      // disable
      for (let item of this.search_result_element_data_arr) {
        let { search_result_element, seller_element, seller_name } = item;
        search_result_element.classList.remove("ubuyassist-hidden");
        search_result_element.classList.remove("ubuyassist-hidden-super");
        search_result_element.querySelector(".ubuy-delete-buttons-wrapper")?.classList.add("ubuy-hidden");
        search_result_element.querySelector(".ubuy-seller-info")?.classList.add("ubuy-hidden");
        seller_element.dataset.ubuy_enabled = "false";
      }
      // stop executing the render because all features should be disabled
      // until the user enables the extension again
      return;
    } else {
      // re-enable
      for (let item of this.search_result_element_data_arr) {
        let { search_result_element, seller_element, seller_name } = item;
        search_result_element.querySelector(".ubuy-delete-buttons-wrapper")?.classList.remove("ubuy-hidden");
        search_result_element.querySelector(".ubuy-seller-info")?.classList.remove("ubuy-hidden");
        seller_element.dataset.ubuy_enabled = "true";
      }
    }
    // create a string that includes enabled seller settings
    // this will be used by css to hide/show components based on enabled settings
    let seller_settings_string = [
      ["showFlag", settings.showFlag],
      ["showCountryIso", settings.showCountryIso],
      ["showStoreIndividual", settings.showStoreIndividual],
      ["showAge", settings.showAge],
      ["showTransactionPercentage", settings.showTransactionPercentage],
    ]
      .filter((item) => {
        return item[1];
      })
      .map((item) => {
        return item[0];
      })
      .join(", ");
    console_log("seller_settings_string", seller_settings_string);
    //
    // Hide elements based on settings
    //
    loop_1: for (let item of this.search_result_element_data_arr) {
      let { shortUser, seller_name, product_id, search_result_element, seller_element } = item;
      // Add item blocking items
      this.addDeleteItemButton(search_result_element, product_id);
      // Update seller info element based on settings
      search_result_element.setAttribute("data-s", "Enabled: " + seller_settings_string);
      // Hide based on blocked country
      if (shortUser) {
        const countryId = shortUser.sc || shortUser.uc;
        if (countryId) {
          // const country = countryData[countryId];
          if (settings.blockedCountries.includes(countryId)) {
            search_result_element.classList.add("ubuyassist-hidden");
            continue loop_1;
          }
        }
      }
      // Hide based on blocked seller
      if (settings.blockSellersEnabled && settings.blockedSellers.includes(seller_name)) {
        search_result_element.classList.add("ubuyassist-hidden");
        continue loop_1;
      }
      // Hide based on blocked poroduct
      if (settings.blockItemsEnabled && settings.blockedItems.includes(product_id)) {
        search_result_element.classList.add("ubuyassist-hidden");
        continue loop_1;
      }
      // Show element if we reached the end of this loop iteration without hiding the search result
      search_result_element.classList.remove("ubuyassist-hidden");
      //
    }
  }
  //
  addDeleteItemButton(element: HTMLElement, product_id: string) {
    // Select item containers from both old and new layouts
    let listItem = element;
    // Find the main item link in either layout
    const sItemLink = listItem.querySelector<HTMLAnchorElement>("a.s-item__link, a.su-link");
    if (!sItemLink || listItem.querySelector(".block-item-btn")) {
      return;
    }

    const imgIconDelete = html_to_element(`
      <img
        src = "${blockIcon}"
        alt = "Block Item"
        title = "Block Item"
        class = "block-item-btn"
        data-product_id = "${product_id}"
      >`);
    const imgIconTemporaryRemove = html_to_element(`
      <img
        src = "${closeIcon}"
        alt = "Temporary Remove"
        title = "Temporary Remove"
        class = "remove-item-btn"
        data-product_id = "${product_id}"
      >`);

    let insertionContainer: Element | null = null;
    if (listItem.matches("li.s-item")) {
      // Old layout: Insert into .s-item__details
      insertionContainer = listItem.querySelector(".s-item__details");
    } else if (listItem.matches("li.su-card-container")) {
      // New layout: Adjust grid and insert into .su-card-container__content
      const attributesContainer = listItem.querySelector<HTMLElement>(".su-card-container__attributes");
      if (attributesContainer) {
        attributesContainer.style.gridTemplateColumns = "35% 55% 10%";
      }
      insertionContainer = listItem.querySelector(".su-card-container__attributes");
    }
    if (insertionContainer) {
      // Add some styling to group the buttons
      const buttonWrapper = document.createElement("div");
      buttonWrapper.classList.add("ubuy-delete-buttons-wrapper");
      //buttonWrapper.style.display = "inline-block"; // Or 'flex' if preferred
      //buttonWrapper.style.marginLeft = "10px"; // Add space from other content
      buttonWrapper.appendChild(imgIconDelete);
      buttonWrapper.appendChild(imgIconTemporaryRemove);
      insertionContainer.appendChild(buttonWrapper);
    }
  }
  // Affiliate logic
  affiliate_show_restriction(search_result_element: HTMLElement, seller_element: HTMLElement) {
    // Iterate over each array of HTMLElement associated with the seller
    // Check if the ancestor does not already contain the activate container
    if (seller_element.parentElement && search_result_element.querySelector(".ubuy-activate-container") === null) {
      seller_element.parentElement.after(
        html_to_element(`
          <span class='ubuy-activate-container'>
            <button class='ubuy-activate-button' title='Activate uBuyAssit and visit our eBay affiliate link.'>Activate</button>
          </span>`),
      );
      // .insertAdjacentHTML("afterend", sellerContent);
    }
  }
  affiliate_hide_restriction(search_result_element: HTMLElement, seller_element: HTMLElement) {
    // Select all elements with the 'ubuy-activate-container' class
    const elementsToRemove = search_result_element.querySelectorAll(".ubuy-activate-container");
    // Remove each element from the DOM
    elementsToRemove.forEach((element) => {
      element.remove();
    });
  }
  // Create fireworks effect
  createFireworks = (element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    const x = rect.left + rect.width / 2;
    const y = rect.top + rect.height / 2;

    // Create multiple particles
    for (let i = 0; i < 50; i++) {
      this.createParticle(x, y);
    }
  };
  // Function to create firework particles
  createParticle = (x: number, y: number) => {
    const particle = document.createElement("div");
    particle.className = "particle";

    // Random angle and distance
    const angle = Math.random() * Math.PI * 2;
    const distance = 50 + Math.random() * 100;

    // Calculate end position
    const tx = Math.cos(angle) * distance;
    const ty = Math.sin(angle) * distance - 50;

    // Random color
    const hue = Math.random() * 360;
    particle.style.backgroundColor = `hsl(${hue}, 100%, 50%)`;

    // Set initial position
    particle.style.left = `${x}px`;
    particle.style.top = `${y}px`;

    // Set custom properties for animation
    particle.style.setProperty("--tx", `${tx}px`);
    particle.style.setProperty("--ty", `${ty}px`);

    // Add animation
    particle.style.animation = "explode 1s ease-out forwards";

    document.body.appendChild(particle);

    // Remove particle after animation
    setTimeout(() => particle.remove(), 1000);
  };
  // Seller info fetching and Cache
  async get_seller_info_from_cache(seller_name: string) {
    // todo: remove seller info from cache after a certain period
    // todo: move these declarations into the config.json file
    const CACHE_DURATION_DAYS = 7;
    const CACHE_DURATION_MS = CACHE_DURATION_DAYS * 24 * 60 * 60 * 1000;
    //
    let key = "_seller_info_" + seller_name;
    let storage = await chrome.storage.local.get([key]);
    if (storage[key] && storage[key].info) {
      return storage[key].info;
    } else {
      return null;
    }
  }
  async get_seller_info_from_cache_or_fetch(seller_name_arr: Array<string>) {
    // todo: remove seller info from cache after a certain period
    // todo: move these declarations into the config.json file
    const CACHE_DURATION_DAYS = 7;
    const CACHE_DURATION_MS = CACHE_DURATION_DAYS * ONE_DAY_IN_MS;
    //
    let seller_name_hash: any = {};
    // Find existing seller names in cache
    let keys = seller_name_arr.map((seller_name) => {
      return "_seller_info_" + seller_name;
    });
    let storage: any = await get_browser_storage(keys);
    let new_seller_names = [];
    for (let seller_name of seller_name_arr) {
      let key = "_seller_info_" + seller_name;
      if (storage[key] === undefined) {
        new_seller_names.push(seller_name);
      } else {
        if (storage[key].created_at + CACHE_DURATION_MS < Date.now()) {
          new_seller_names.push(seller_name);
        } else {
          seller_name_hash[seller_name] = storage[key].info;
        }
      }
    }
    // Fetch seller info for seller names that were not found in cache
    let new_storage: any = {};
    if (new_seller_names.length > 0) {
      let result = await this.fetch_sellers(new_seller_names);
      for (let seller_name in result) {
        let key = "_seller_info_" + seller_name;
        new_storage[key] = {
          info: result[seller_name],
          created_at: Date.now(),
        };
        seller_name_hash[seller_name] = result[seller_name];
      }
      await set_browser_storage(new_storage);
    }
    //
    return seller_name_hash;
    //
  }
  async fetch_sellers(sellers: string[]): Promise<Record<string, ShortUser>> {
    const sellerNamesParam = sellers.join(",");
    const url = `https://userinfo.ubuyfirst.net/v1/userinfo?data=${encodeURIComponent(sellerNamesParam)}`;
    return await sendChromeMessage({
      action: "fetchSellers",
      url: url,
    });
  }
}
