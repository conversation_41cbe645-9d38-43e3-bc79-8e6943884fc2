import React, { useEffect, useState } from "react";
import Checkbox from "./Checkbox";
import TextArea from "./TextArea";
import supabase from "../../helper/supabaseClient";
import { get_browser_storage } from "../browser_storage";

import { useUserSettings } from "../../helper/UserSettingsContext";
import { console_log } from "../../helper/helpers";

interface BlockItemsListProps {
  toggleState: boolean;
}

const BlockItemsList: React.FC<BlockItemsListProps> = ({ toggleState }) => {
  const { userSettings, update_user_settings } = useUserSettings();

  const [blockItemData, setBlockItemData] = useState<any[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState<boolean>(true);
  const [selectAll, setSelectAll] = useState(false);
  const [showBulkAddArea, setShowBulkAddArea] = useState(false); // State for bulk add UI visibility
  const [bulkAddInput, setBulkAddInput] = useState(""); // State for bulk add text area
  const [isAddingBulk, setIsAddingBulk] = useState(false); // Loading state for bulk add
  const [active_section_name, set_active_section_name] = useState("normal");

  useEffect(() => {
    let data = userSettings.blockedItems.map((id) => {
      return { id };
    });
    setBlockItemData(data);
  }, [userSettings]);

  useEffect(() => {
    if (userSettings.toggleState && userSettings.blockItemsEnabled) {
      if (userSettings.blockedItems.length === 0) {
        set_active_section_name("no_items");
      } else {
        set_active_section_name("normal");
      }
    } else {
      set_active_section_name("disabled");
    }
  }, [userSettings]);

  const handleSelectItem = (item_id: string) => {
    const newSelectedItems = new Set(selectedItems);
    if (newSelectedItems.has(item_id)) {
      newSelectedItems.delete(item_id);
    } else {
      newSelectedItems.add(item_id);
    }
    setSelectedItems(newSelectedItems);
  };

  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedItems(new Set());
    } else {
      const allIds = new Set(blockItemData.map((item) => item.id.toString()));
      setSelectedItems(allIds);
    }
    setSelectAll(!selectAll);
  };

  const removeItems = async () => {
    // Get selected item names as an array
    const selected_names = Array.from(selectedItems);
    console_log("selected_names", selected_names);

    userSettings.blockedItems = userSettings.blockedItems.filter((item_name) => {
      return selected_names.includes(item_name) === false;
    });

    update_user_settings({ ...userSettings });
  };

  // --- Bulk Add Handlers ---
  const handleBulkAddClick = () => {
    setShowBulkAddArea(true);
  };

  const handleCancelBulkAdd = () => {
    setShowBulkAddArea(false);
    setBulkAddInput("");
    setIsAddingBulk(false);
  };

  const handleAddToList = async () => {
    if (!bulkAddInput.trim()) {
      alert("Please paste items into the text area.");
      return;
    }
    setIsAddingBulk(true);
    try {
      const itemsToAdd = bulkAddInput
        .split("\n")
        .map((s) => s.trim())
        .filter((s) => s !== ""); // Split, trim, and remove empty lines

      console_log("itemsToAdd", itemsToAdd);

      for (let item of itemsToAdd) {
        if (userSettings.blockedItems.includes(item) === false) {
          userSettings.blockedItems.push(item);
        }
      }

      update_user_settings({ ...userSettings });

      setBulkAddInput(""); // Clear input
      setShowBulkAddArea(false); // Hide area
    } catch (error: any) {
      console_log("Error adding items:", error);
      alert(`Error adding items: ${error.message || "Unknown error"}`);
    } finally {
      setIsAddingBulk(false); // End loading state
    }
  };

  // --- End Bulk Add Handlers ---

  return (
    <>
      <h6 className="text-sm font-bold mt-6 mb-2">Block Items List ({userSettings.blockedItems.length})</h6>
      <Checkbox
        id="enable-block-items-list"
        label="Enable Block Items List"
        checked={userSettings.blockItemsEnabled}
        onChange={() => update_user_settings({ ...userSettings, blockItemsEnabled: !userSettings.blockItemsEnabled })}
        disabled={toggleState}
      />

      <div className="mb-3"></div>
      {active_section_name === "initial" && <div>Initial</div>}
      {active_section_name === "disabled" && <div>Feature disabled</div>}
      {active_section_name === "no_items" && (
        <div>
          {/* Wrap "No BlockItems" and Bulk Add button */}
          <p>No Blocked Items found.</p>
          <button
            className="mt-2 p-2 border rounded-lg text-green-500 border-green-500"
            onClick={handleBulkAddClick}
            disabled={showBulkAddArea}
          >
            Bulk Add Items
          </button>
          {/* Conditional Bulk Add Area (also shown when list is empty) */}
          {showBulkAddArea && (
            <div className="mt-4 p-4 border rounded-lg bg-gray-700 space-y-2">
              <label htmlFor="bulk-add-items-empty" className="block text-sm font-medium">
                Paste Item IDs (one per line):
              </label>
              <TextArea
                id="bulk-add-items-empty"
                placeholder={"123456789012\n210987654321"}
                value={bulkAddInput}
                onChange={(e) => setBulkAddInput(e.target.value)}
                disabled={isAddingBulk}
                rows={5}
              />
              <div className="flex space-x-2">
                <button
                  className="p-2 border rounded-lg bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                  onClick={handleAddToList}
                  disabled={isAddingBulk || !bulkAddInput.trim()}
                >
                  {isAddingBulk ? "Adding..." : "Add to List"}
                </button>
                <button
                  className="p-2 border rounded-lg bg-gray-500 hover:bg-gray-600"
                  onClick={handleCancelBulkAdd}
                  disabled={isAddingBulk}
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
      )}
      {active_section_name === "normal" && (
        <div className="space-y-4">
          {" "}
          {/* Added spacing */}
          {/* Existing List Management */}
          <div>
            <div className="flex space-x-2 mb-4">
              {" "}
              {/* Button row */}
              <button className="p-2 border rounded-lg" onClick={toggleSelectAll}>
                {selectAll ? "Deselect All" : "Select All"}
              </button>
              <button
                className="p-2 border rounded-lg text-red-500 border-red-500"
                disabled={selectedItems.size === 0}
                onClick={removeItems}
              >
                Remove Selected ({selectedItems.size})
              </button>
              <button
                className="p-2 border rounded-lg text-green-500 border-green-500"
                onClick={handleBulkAddClick}
                disabled={showBulkAddArea} // Disable if area is already shown
              >
                Bulk Add Items
              </button>
            </div>

            {/* Bulk Add Area (Conditional) */}
            {showBulkAddArea && (
              <div className="p-4 border rounded-lg bg-gray-700 space-y-2">
                <label htmlFor="bulk-add-items" className="block text-sm font-medium">
                  Paste Item IDs (one per line):
                </label>
                <TextArea
                  id="bulk-add-items"
                  placeholder={"123456789012\n210987654321"}
                  value={bulkAddInput}
                  onChange={(e) => setBulkAddInput(e.target.value)}
                  disabled={isAddingBulk}
                  rows={5}
                />
                <div className="flex space-x-2">
                  <button
                    className="p-2 border rounded-lg bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                    onClick={handleAddToList}
                    disabled={isAddingBulk || !bulkAddInput.trim()}
                  >
                    {isAddingBulk ? "Adding..." : "Add to List"}
                  </button>
                  <button
                    className="p-2 border rounded-lg bg-gray-500 hover:bg-gray-600"
                    onClick={handleCancelBulkAdd}
                    disabled={isAddingBulk}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}

            {/* Item List */}
            <ul className="max-h-60 overflow-y-auto border rounded-lg p-2">
              {" "}
              {/* Added scroll */}
              {blockItemData.map(
                (
                  item, // Removed index from map if id is unique
                ) => (
                  <li
                    key={item.id} // Use id as key
                    className="flex items-center justify-between p-2 mb-1 rounded bg-white/10 hover:bg-white/20"
                  >
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-blue-600"
                        checked={selectedItems.has(item.id.toString())}
                        onChange={() => handleSelectItem(item.id.toString())}
                      />
                      <span className="truncate" title={item.id.toString()}>
                        {item.id}
                      </span>{" "}
                      {/* Added truncate and title */}
                    </label>
                  </li>
                ),
              )}
            </ul>
          </div>
        </div>
      )}
    </>
  );
};

export default BlockItemsList;
