import React, { useMemo, useState, useEffect } from "react";
import Select, { type MultiValue } from "react-select";
import type { CountryData, CountryInfo } from "../files/types";
import { countryData, allSubRegionOptions } from "../files/countryData";
import { set_browser_storage } from "../browser_storage";
import { useUserSettings } from "../../helper/UserSettingsContext";
import { console_log } from "../../helper/helpers";

const recycleBinIcon =
  "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGQ9Ik00IDZIMjBNMTYgNkwxNS43Mjk0IDUuMTg4MDdDMTUuNDY3MSA0LjQwMTI1IDE1LjMzNTkgNC4wMDc4NCAxNS4wOTI3IDMuNzE2OThDMTQuODc3OSAzLjQ2MDEzIDE0LjYwMjEgMy4yNjEzMiAxNC4yOTA1IDMuMTM4NzhDMTMuOTM3NiAzIDEzLjUyMyAzIDEyLjY5MzYgM0gxMS4zMDY0QzEwLjQ3NyAzIDEwLjA2MjQgMyA5LjcwOTUxIDMuMTM4NzhDOS4zOTc5MiAzLjI2MTMyIDkuMTIyMDggMy40NjAxMyA4LjkwNzI5IDMuNzE2OThDOC42NjQwNSA0LjAwNzg0IDguNTMyOTIgNC40MDEyNSA4LjI3MDY0IDUuMTg4MDdMOCA2TTE4IDZWMTYuMkMxOCAxNy44ODAyIDE4IDE4LjcyMDIgMTcuNjczIDE5LjM2MkMxNy4zODU0IDE5LjkyNjUgMTYuOTI2NSAyMC4zODU0IDE2LjM2MiAyMC42NzNDMTUuNzIwMiAyMSAxNC44ODAyIDIxIDEzLjIgMjFIMTAuOEM5LjExOTg0IDIxIDguMjc5NzYgMjEgNy42MzgwMyAyMC42NzNDNy4wNzM1NCAyMC4zODU0IDYuNjE0NiAxOS45MjY1IDYuMzI2OTggMTkuMzYyQzYgMTguNzIwMiA2IDE3Ljg4MDIgNiAxNi4yVjZNMTQgMTBWMTdNMTAgMTBWMTciIHN0cm9rZT0iI2ZmZmZmZiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4NCjwvc3ZnPg==";

interface CountryListProps {}

const subRegionOrder = [
  "Northern America",
  "Latin America and the Caribbean",
  "Western Europe",
  "Northern Europe",
  "Southern Europe",
  "Eastern Europe",
  "Australia and New Zealand",
  "Eastern Asia",
  "South-eastern Asia",
  "Southern Asia",
  "Western Asia",
  "Central Asia",
  "Northern Africa",
  "Sub-Saharan Africa",
  "Polynesia",
  "Micronesia",
  "Melanesia",
];

type Option = {
  value: number;
  label: string;
};

let allCountryOptions: Array<Option> = [];
let allCountryInfo: Array<CountryInfo> = [];

for (let id in countryData) {
  let country = countryData[id];
  allCountryOptions.push({
    value: country.id,
    label: country.name,
  });
  allCountryInfo.push(country);
}

function get_flag_url(country_id: any) {
  return `/images/flags/${countryData[country_id].iso}.SVG`;
}

const CountryList: React.FC<CountryListProps> = ({}) => {
  const { userSettings, update_user_settings } = useUserSettings();
  // const allCountries: CountryInfo[] = useMemo(() => {
  //   return Object.values(countryData as CountryData);
  // }, []);

  // const allSubRegions: CountryInfo[] = useMemo(() => {
  //   console_log("allcountries", allCountries);
  //   let arr = subRegionOrder.map((label) => {
  //     return { label, value: 0 };
  //   });
  //   loop_1: for (let item of arr) {
  //     for (let country of allCountries) {
  //       if (country.subRegion === item.label) {
  //         item.value = country.subRegionId;
  //         continue loop_1;
  //       }
  //     }
  //   }
  //   console_log("allsubreg", arr);
  // }, []);

  const [selectedSubRegionOptions, setSelectedSubRegionOptions] = useState<Array<Option>>([]);
  const [selectedCountryOptions, setSelectedCountryOptions] = useState<Array<Option>>([]);
  const [notSelectedCountryOptions, setNotSelectedCountryOptions] = useState<Array<Option>>([]);
  // const [countryOptions, setCountryOptions] = useState<Array<Option>>([]);
  const [subRegionOptions, setSubRegionOptions] = useState<Array<Option>>([]);

  // Filter countries that are not blocked for the dropdown options
  // const availableCountries = allCountries.filter((country) => {
  //   return userSettings.blockedCountries.includes(country.id) === false;
  // });

  // const blockedCountries = allCountries.filter((country) => {
  //   return userSettings.blockedCountries.includes(country.id) === true;
  // });

  // Load selected sub-regions and countries from browser storage
  useEffect(() => {
    // setSelectedSubRegions(userSettings.blockedRegions);
    // setSelectedCountryOptions(userSettings.blockedCountries);

    // const subRegionOptions = allSubRegionOptions.map((subRegion) => ({
    //   value: subRegion.value,
    //   label: subRegion.label,
    // }));
    // const countryOptions = allCountries.map((country) => ({
    //   value: country.id,
    //   label: country.name,
    // }));

    let selectedSubRegionOptions = allSubRegionOptions.filter((option) => {
      return userSettings.blockedSubRegions.includes(option.value);
    });
    let selectedCountryOptions = allCountryOptions.filter((option) => {
      return userSettings.blockedCountries.includes(option.value) === true;
    });
    let notSelectedCountryOptions = allCountryOptions.filter((option) => {
      return userSettings.blockedCountries.includes(option.value) === false;
    });

    // console_log("availableCountries", availableCountries);
    // console_log("countryOptions", countryOptions);
    console_log("userSettings.blockedSubRegions", userSettings.blockedSubRegions);
    // console_log("userSettings.blockedCountries", userSettings.blockedCountries);
    console_log("selectedSubRegionOptions", selectedSubRegionOptions);
    // console_log("selectedCountryOptions", selectedCountryOptions);
    //
    // setCountryOptions(countryOptions);
    //
    setSubRegionOptions(subRegionOptions);
    setSelectedSubRegionOptions(selectedSubRegionOptions);
    setSelectedCountryOptions(selectedCountryOptions);
    setNotSelectedCountryOptions(notSelectedCountryOptions);
    //
    //
  }, [userSettings]);

  // Block all countries from a sub-region
  const handleSubRegionSelectChange = (selectedOptions: MultiValue<Option>, actionMeta: any) => {
    //

    console_log("selectedOptions", selectedOptions);
    console_log("actionMeta", actionMeta);
    //
    if (actionMeta.action === "select-option") {
      let selected_option = actionMeta.option as Option;
      if (userSettings.blockedSubRegions.includes(selected_option.value) === false) {
        userSettings.blockedSubRegions.push(selected_option.value);
      }
      for (let countryInfo of allCountryInfo) {
        if (
          //
          countryInfo.subRegionId === selected_option.value &&
          userSettings.blockedCountries.includes(countryInfo.id) === false
        ) {
          userSettings.blockedCountries.push(countryInfo.id);
        }
      }
      update_user_settings({ ...userSettings });
    } else if (actionMeta.action === "remove-value") {
      let removed_option = actionMeta.removedValue as Option;
      userSettings.blockedSubRegions = userSettings.blockedSubRegions.filter((sub_region_id) => {
        return sub_region_id !== removed_option.value;
      });
      userSettings.blockedCountries = userSettings.blockedCountries.filter((country_id) => {
        let country_data = countryData[country_id];
        return country_data.subRegionId !== removed_option.value;
      });
      update_user_settings({ ...userSettings });
    }
  };

  // Handle multi-country block/unblock with multi-value select
  const handleCountrySelectChange = (selectedOptions: MultiValue<Option>, actionMeta: any) => {
    console_log("handleCountrySelectChange");
    console_log("selectedOptions", selectedOptions);
    console_log("actionMeta", actionMeta);
    let blockedCountries = selectedOptions.map((option) => {
      return option.value;
    });
    console_log("updating");
    update_user_settings({ ...userSettings, blockedCountries });
    //
    // const newRemovedCountries = { ...removedCountries };
    // const options = selectedOptions ? [...selectedOptions] : [];

    // // Update the selected country options state
    // setSelectedCountryOptions(options);

    // // Save selected country options to browser storage
    // set_browser_storage({
    //   selectedCountryOptions: options,
    // });

    // if (actionMeta.action === "remove-value") {
    //   // If a country was removed from selection, unblock it
    //   const removedCountryId = actionMeta.removedValue.value;
    //   newRemovedCountries[removedCountryId] = false;
    // } else if (actionMeta.action === "select-option") {
    //   // If a country was selected, block it
    //   const addedCountryId = actionMeta.option.value;
    //   newRemovedCountries[addedCountryId] = true;
    // } else if (actionMeta.action === "clear") {
    //   // Handle clear action - unblock all selected countries
    //   selectedCountryOptions.forEach((option) => {
    //     newRemovedCountries[option.value] = false;
    //   });
    // }

    // setRemovedCountries(newRemovedCountries);
  };

  // Unblock all countries
  const unblockAllCountries = () => {
    // This will trigger a re-render and a useEffect handler defined above
    // so we don't need to update selectedCountryOptions or selectedSubRegionOptions here
    // because react will "react" to changes in userSettings
    console_log("updating");
    update_user_settings({
      ...userSettings,
      blockedCountries: [],
      blockedSubRegions: [],
    });
  };

  // Renamed function to reflect its new purpose: only unblocking
  const handleUnblockCountry = (countryId: number) => {
    console_log("updating");
    userSettings.blockedCountries = userSettings.blockedCountries.filter((country_id) => {
      return countryId !== country_id;
    });
    update_user_settings({
      ...userSettings,
      blockedCountries: userSettings.blockedCountries,
    });
  };

  // Custom styles for react-select (dark background, white text)
  const customStyles = {
    control: (base: any, state: any) => ({
      ...base,
      "backgroundColor": "#1f2937", // Tailwind gray-800
      "color": "#ffffff",
      "borderRadius": "8px", // Rounded border for modern design
      "borderColor": state.isFocused ? "#3b82f6" : "#374151", // Blue on focus, gray otherwise
      "boxShadow": state.isFocused ? "0 0 0 3px rgba(59, 130, 246, 0.5)" : "none", // Tailwind blue-500
      "&:hover": {
        borderColor: "#3b82f6", // Blue border on hover
      },
    }),
    singleValue: (base: any) => ({
      ...base,
      color: "#ffffff",
    }),
    input: (base: any) => ({
      ...base,
      color: "#ffffff",
    }),
    menu: (base: any) => ({
      ...base,
      backgroundColor: "#1f2937",
      borderRadius: "8px", // Rounded dropdown
      boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
    }),
    option: (base: any, { isFocused, isSelected }: { isFocused: boolean; isSelected: boolean }) => ({
      ...base,
      "backgroundColor": isSelected ? "#3b82f6" : isFocused ? "#374151" : "#1f2937",
      "color": isSelected ? "#ffffff" : isFocused ? "#d1d5db" : "#ffffff",
      "cursor": "pointer",
      "padding": "10px",
      "borderRadius": "5px", // Rounded items
      "&:hover": {
        backgroundColor: "#3b82f6",
        color: "#ffffff",
      },
    }),
    multiValue: (base: any) => ({
      ...base,
      backgroundColor: "#3b82f6", // Tailwind blue-500
      color: "#ffffff",
      borderRadius: "5px",
      padding: "3px 6px",
    }),
    multiValueLabel: (base: any) => ({
      ...base,
      color: "#ffffff",
    }),
    multiValueRemove: (base: any) => ({
      ...base,
      "color": "#ffffff",
      "&:hover": {
        backgroundColor: "#2563eb", // Tailwind blue-600
        color: "#ffffff",
      },
    }),
    placeholder: (base: any) => ({
      ...base,
      color: "#9ca3af", // Tailwind gray-400
    }),
  };

  return (
    <div className="w-full pl-1">
      <h6 className="text-sm font-bold text-white mb-2">Hide sub-region or a country</h6>

      {/* Select for Sub-Region */}
      <div className="mb-4">
        <Select
          options={allSubRegionOptions}
          onChange={handleSubRegionSelectChange}
          placeholder="Select Sub-Regions"
          isClearable={false}
          isMulti
          styles={customStyles}
          isDisabled={!userSettings.toggleState}
          value={selectedSubRegionOptions}
        />
      </div>

      {/* Select for Individual Countries */}
      <div className="mb-4">
        <Select
          options={notSelectedCountryOptions}
          onChange={handleCountrySelectChange}
          placeholder="Search and block/unblock country..."
          isClearable={false}
          isMulti
          styles={customStyles}
          isDisabled={!userSettings.toggleState}
          value={selectedCountryOptions}
        />
      </div>

      {/* Blocked Countries List */}
      {userSettings.toggleState && (
        <div>
          <div className="flex items-center mb-2">
            <h6 className="text-sm font-semibold text-white">Blocked Countries:</h6>
            {/* Unblock All Countries Link */}
            {selectedCountryOptions.length > 0 && (
              <div className="ml-4">
                <button
                  onClick={unblockAllCountries}
                  className="text-blue-400 text-xs hover:underline"
                  disabled={!userSettings.toggleState}
                >
                  Unblock All Countries
                </button>
              </div>
            )}
          </div>
          {selectedCountryOptions.length === 0 ? (
            <p className="text-sm text-gray-400">No countries blocked.</p>
          ) : (
            <ul className="list-disc list-inside text-sm text-white space-y-1">
              {selectedCountryOptions.map((country_option) => (
                <li key={country_option.value} className="flex justify-between items-center">
                  {/* Country flag */}
                  <img className="country-list-flag-image" src={get_flag_url(country_option.value)}></img>
                  <span>{country_option.label}</span>
                  <span className="flex-grow"></span>
                  {/* Icon to unblock */}
                  <img
                    src={recycleBinIcon}
                    alt="Unblock Country"
                    onClick={() => userSettings.toggleState && handleUnblockCountry(country_option.value)} // Use the renamed handler
                    className={`cursor-pointer w-4 h-4 ${
                      !userSettings.toggleState ? "opacity-50 cursor-not-allowed" : ""
                    }`} // Basic styling and disabled state
                    style={{ filter: !userSettings.toggleState ? "grayscale(100%)" : "none" }} // Visual cue for disabled
                  />
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default CountryList;
