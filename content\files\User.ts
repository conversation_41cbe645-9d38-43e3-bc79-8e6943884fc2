export type User = {
    account_active: boolean; // assuming `a` can determine active status
    userName?: string; // Mapped from `u`
    verified?: boolean; // Mapped from `v`
    feedbackPrivate?: boolean; // Mapped from `p`
    eais?: string; // Mapped from `e`
    transactionPercent?: number; // Mapped from `tp`
    business?: boolean; // Mapped from `b`
    storeOwner?: boolean; // Mapped from `s`
    storeName?: string; // Mapped from `sn`
    topRated?: boolean; // Mapped from `t`
    user_web_country_id?: number; // Mapped from `uc`
    store_web_country_id?: number; // Mapped from `sc`
    user_api_country_id?: number; // duplicate or additional ID field if needed
    store_api_country_id?: number; // duplicate or additional ID field if needed
    regDate?: string; // Mapped from `rd`
  };


  export type ShortUser = {
    a?: number;
    u?: string; // User name
    v?: number; // ID verification status
    p?: number; // ID verification status
    e?: string; // Single EIAS Token
    tp?: number; // Transaction percentage
    b?: number; // Business seller status
    s?: number; // Store owner status
    sn?: string; // Store name
    t?: number; // Top Rated Seller status
    uc?: number; // Site ID, string or number
    sc?: number; // Site ID, string or number
    rd?: string; // Seller registration date
  };