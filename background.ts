import { default_user_settings } from "./content/files/types";

chrome.runtime.onInstalled.addListener(async () => {
  let storage = await chrome.storage.local.get(["browser_user_settings"]);
  if (storage.browser_user_settings === undefined) {
    let browser_user_settings = default_user_settings;
    browser_user_settings.lastClickTime = Date.now();
    await chrome.storage.local.set({ browser_user_settings });
  }
});

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "openInBackground" && request.url) {
    // Open the tab in the background and pin it
    openPinnedTabWithAutoClose(request.url, 5000);
  } else if (request.action === "fetchSellers" && request.url) {
    // Handle fetchSellers request
    fetch(request.url)
      .then((response) => response.json())
      .then((data) => sendResponse(data))
      .catch((error) => {
        console.error("Error fetching sellers:", error);
        sendResponse({});
      });
    return true; // Keep the message channel open for async response
  }
});

function openPinnedTabWithAutoClose(url: any, autoCloseTimeout = 5000) {
  // This function opens a pinned tab in the background and closes it after a set timeout.

  chrome.tabs.create({ url: url, active: false, pinned: true }, (tab) => {
    if (chrome.runtime.lastError) {
      // Log any errors that occur during tab creation
      console.error(`Error creating tab: ${chrome.runtime.lastError.message}`);
      return;
    }

    if (tab && tab.id) {
      // If tab was successfully created, set a timeout to close it
      const timeoutId = setTimeout(() => {
        closeTabWithCleanup(tab.id);
      }, autoCloseTimeout);

      // Store timeoutId for potential cleanup if needed
      storeTabTimeout(tab.id, timeoutId);
    }
  });
}

function closeTabWithCleanup(tabId: number | undefined) {
  // This function handles closing a tab and cleaning up any associated data.

  if (typeof tabId === "number") {
    chrome.tabs.remove(tabId, () => {
      if (chrome.runtime.lastError) {
        // Log any errors that occur during tab removal
        console.error(chrome.runtime.lastError.message);
      }
    });
  } else {
    // Handle the case where tabId is undefined (if necessary)
    console.error("Tab ID is undefined, cannot remove tab.");
  }
}

const tabTimeoutMap = new Map(); // A map to store timeout IDs associated with each tab

function storeTabTimeout(tabId: number, timeoutId: NodeJS.Timeout) {
  // Stores the timeout ID for a specific tab
  tabTimeoutMap.set(tabId, timeoutId);
}

//
async function check_browser_storage_size() {
  let bytes_in_use = await chrome.storage.local.getBytesInUse();
  let max_bytes_in_use = 125 * 1024 * 1024;
  //
  if (bytes_in_use > max_bytes_in_use) {
    // todo: move these keys as constants in types.ts instead of plain strings
    let storage_to_preserve = await chrome.storage.local.get([
      "session",
      "browser_user_settings",
      "supabase_user_settings",
    ]);
    await chrome.storage.local.clear();
    await chrome.storage.local.set(storage_to_preserve);
  }
}
function main() {
  // Create an alarm to check for browser storage size
  chrome.alarms.create("ALARM_CHECK_BROWSER_STORAGE_SIZE", {
    periodInMinutes: 60,
  });
  chrome.alarms.onAlarm.addListener(async (alarm) => {
    if (alarm.name === "ALARM_CHECK_BROWSER_STORAGE_SIZE") {
      check_browser_storage_size();
    }
  });
  // Also call it immediately
  check_browser_storage_size();
}
main();
