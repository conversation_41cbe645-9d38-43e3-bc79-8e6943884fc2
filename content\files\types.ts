import type { ShortUser } from "./User";

// types.ts

export const ONE_DAY_IN_MS = 24 * 60 * 60 * 1000;

export type SupabaseUserSession = {
  id: string;
  token: string;
  refreshToken: string;
};

export type BrowserStorage = {
  session: SupabaseUserSession | null;
  // Information from chrome.storage.local
  // saved before user is logged in
  // when they log out - use this storage
  browser_user_settings: UserSettings | null;
  // Information from the UserSettings table
  // fetched for the currently authenticated user
  supabase_user_settings: UserSettings | null;
};

export interface SellerDBRecord {
  name: string;
  info: ShortUser;
  timestamp: number;
}

export type UserSettings = {
  // disables or enables ALL extension features
  toggleState: boolean;
  // The last time ( in milliseconds ) of when the extension was "Activated"
  // Activating the extnesion opens a new tab that adds affiliate cookies
  lastClickTime: number;
  //
  blockedItems: Array<string>;
  blockedSellers: Array<string>;
  blockedCountries: Array<number>;
  // Each country has a "region" and a "subRegion"
  // it is possible to block a region and then unblock each country one by one
  blockedRegions: Array<number>;
  blockedSubRegions: Array<number>;
  //
  // old legacy settings:
  blockSellersEnabled: boolean;
  blockItemsEnabled: boolean;
  showFlag: boolean;
  showStoreIndividual: boolean;
  showAge: boolean;
  showTransactionPercentage: boolean;
  showCountryIso: boolean;
  //
};

export const default_user_settings: UserSettings = {
  //
  toggleState: true,
  lastClickTime: 0,
  //
  blockedItems: [],
  blockedSellers: [],
  blockedCountries: [],
  blockedRegions: [],
  blockedSubRegions: [],
  //
  blockSellersEnabled: true,
  blockItemsEnabled: true,
  showFlag: true,
  showStoreIndividual: true,
  showAge: true,
  showTransactionPercentage: true,
  showCountryIso: true,
  //
};

export type Settings = UserSettings;

export interface CountryInfo {
  name: string;
  iso: string;
  id: number;
  region: string;
  regionId: number;
  subRegion: string;
  subRegionId: number;
}

export type CountryData = {
  [key: number]: CountryInfo;
};
