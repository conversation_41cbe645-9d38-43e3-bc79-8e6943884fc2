import supabase from "./supabaseClient";
import { get_browser_storage, set_browser_storage } from "../content/browser_storage";
import { console_log } from "./helpers";
import type { SupabaseUserSession } from "../content/files/types";

class SessionManager {
  private static instance: SessionManager;
  private isInitializing = false;
  private currentSession: SupabaseUserSession | null = null;
  private authStateListener: any = null;

  private constructor() {
    this.setupAuthStateListener();
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  private setupAuthStateListener() {
    // Listen for auth state changes to update stored tokens
    this.authStateListener = supabase.auth.onAuthStateChange(async (event, session) => {
      console_log("Auth state change:", event, session);

      if (event === 'TOKEN_REFRESHED' && session) {
        // Update stored tokens when they're refreshed
        const updatedSession: SupabaseUserSession = {
          id: session.user.id,
          token: session.access_token,
          refreshToken: session.refresh_token,
        };

        await set_browser_storage({ session: updatedSession });
        this.currentSession = updatedSession;
        console_log("Tokens updated after refresh:", updatedSession.refreshToken);
      } else if (event === 'SIGNED_OUT') {
        // Clear session when signed out
        await this.clearSession();
      } else if (event === 'SIGNED_IN' && session) {
        // Update session when signed in
        const newSession: SupabaseUserSession = {
          id: session.user.id,
          token: session.access_token,
          refreshToken: session.refresh_token,
        };

        await set_browser_storage({ session: newSession });
        this.currentSession = newSession;
        console_log("Session updated after sign in:", newSession.refreshToken);
      }
    });
  }

  public async initializeSession(): Promise<SupabaseUserSession | null> {
    // Prevent multiple simultaneous initialization attempts
    if (this.isInitializing) {
      console_log("Session initialization already in progress, waiting...");
      // Wait for current initialization to complete
      while (this.isInitializing) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.currentSession;
    }

    this.isInitializing = true;

    try {
      const browser_storage = await get_browser_storage(["session"]);
      
      if (!browser_storage.session) {
        console_log("No session found in storage");
        this.isInitializing = false;
        return null;
      }

      // Check if we already have a valid session
      const { data: { session: currentSupabaseSession } } = await supabase.auth.getSession();
      if (currentSupabaseSession && currentSupabaseSession.user.id === browser_storage.session.id) {
        console_log("Valid session already exists");
        this.currentSession = browser_storage.session;
        this.isInitializing = false;
        return this.currentSession;
      }

      // Try to restore session
      console_log("Attempting to restore session with refresh token:", browser_storage.session.refreshToken);
      const { data, error } = await supabase.auth.setSession({
        access_token: browser_storage.session.token,
        refresh_token: browser_storage.session.refreshToken,
      });

      if (error) {
        console_log("Session restoration failed:", error.message);

        if (error.message.includes("refresh_token_already_used") ||
            error.message.includes("Invalid Refresh Token") ||
            error.message.includes("refresh_token_not_found")) {
          // Clear invalid session
          console_log("Clearing invalid session due to refresh token error");
          await this.clearSession();
          this.isInitializing = false;
          return null;
        }

        throw error;
      }

      if (data.session) {
        // Update stored session with potentially new tokens
        const updatedSession: SupabaseUserSession = {
          id: data.session.user.id,
          token: data.session.access_token,
          refreshToken: data.session.refresh_token,
        };
        
        await set_browser_storage({ session: updatedSession });
        this.currentSession = updatedSession;
        console_log("Session restored successfully");
      }

      this.isInitializing = false;
      return this.currentSession;

    } catch (error) {
      console_log("Error initializing session:", error);
      await this.clearSession();
      this.isInitializing = false;
      return null;
    }
  }

  public async clearSession(): Promise<void> {
    console_log("Clearing session");
    this.currentSession = null;
    await set_browser_storage({ 
      session: null, 
      supabase_user_settings: null 
    });
  }

  public async getValidSession(): Promise<SupabaseUserSession | null> {
    // Always check browser storage for the latest session to avoid stale tokens
    const browser_storage = await get_browser_storage(["session"]);

    if (browser_storage.session) {
      // Update current session if browser storage has newer tokens
      if (!this.currentSession ||
          this.currentSession.refreshToken !== browser_storage.session.refreshToken) {
        console_log("Updating session from browser storage");
        this.currentSession = browser_storage.session;
      }
      return this.currentSession;
    }

    if (this.currentSession) {
      return this.currentSession;
    }

    return await this.initializeSession();
  }

  public async ensureValidSession(): Promise<boolean> {
    const session = await this.getValidSession();
    return session !== null;
  }

  public getCurrentSession(): SupabaseUserSession | null {
    return this.currentSession;
  }

  public setCurrentSession(session: SupabaseUserSession | null): void {
    this.currentSession = session;
  }

  public async forceRefreshFromStorage(): Promise<SupabaseUserSession | null> {
    console_log("Force refreshing session from storage");
    const browser_storage = await get_browser_storage(["session"]);

    if (browser_storage.session) {
      this.currentSession = browser_storage.session;
      console_log("Session force refreshed:", this.currentSession.refreshToken);
      return this.currentSession;
    }

    this.currentSession = null;
    return null;
  }

  public destroy(): void {
    if (this.authStateListener) {
      this.authStateListener.data.subscription.unsubscribe();
      this.authStateListener = null;
    }
  }
}

export default SessionManager;
